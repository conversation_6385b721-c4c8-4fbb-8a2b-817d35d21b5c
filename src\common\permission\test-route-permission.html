<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由权限管理器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <h1>路由权限管理器测试</h1>

    <div class="test-section">
        <h2>1. 基础功能测试</h2>
        <button class="btn-primary" onclick="testBasicFunctions()">运行基础测试</button>
        <div id="basic-results"></div>
    </div>

    <div class="test-section">
        <h2>2. 路由权限检查测试</h2>
        <button class="btn-primary" onclick="testRoutePermissions()">测试路由权限</button>
        <div id="route-results"></div>
    </div>

    <div class="test-section">
        <h2>3. 用户角色模拟测试</h2>
        <button class="btn-secondary" onclick="simulateUser(0)">模拟普通用户</button>
        <button class="btn-secondary" onclick="simulateUser(1)">模拟管理员</button>
        <button class="btn-secondary" onclick="simulateUser(2)">模拟超级管理员</button>
        <div id="user-simulation-results"></div>
    </div>

    <div class="test-section">
        <h2>4. 白名单测试</h2>
        <button class="btn-primary" onclick="testWhiteList()">测试白名单路由</button>
        <div id="whitelist-results"></div>
    </div>

    <script type="module">
        // 模拟必要的全局对象
        window.Tool = {
            getToken: () => 'mock-token-123',
            isInit: () => true
        };

        // 模拟用户角色常量
        window.USER_ROLE = {
            NORMAL: 0,
            ADMIN: 1,
            SUPER_ADMIN: 2,
            DIRECTOR: 3
        };

        // 导入权限管理器
        import RoutePermissionManager from './RoutePermissionManager.js';

        let routeManager;
        let currentUser = { uid: 'test-user', role: 1 };

        // 初始化权限管理器
        async function initializeManager() {
            try {
                routeManager = new RoutePermissionManager();

                // 模拟用户信息
                routeManager.userInfo = currentUser;
                routeManager.initialized = true;

                // 加载权限配置
                await routeManager.loadPermissions();

                return true;
            } catch (error) {
                console.error('初始化权限管理器失败:', error);
                return false;
            }
        }

        // 基础功能测试
        window.testBasicFunctions = async function() {
            const resultsDiv = document.getElementById('basic-results');
            resultsDiv.innerHTML = '<div class="info">正在运行基础测试...</div>';

            const results = [];

            try {
                // 初始化管理器
                const initSuccess = await initializeManager();
                results.push({
                    test: '权限管理器初始化',
                    success: initSuccess,
                    message: initSuccess ? '初始化成功' : '初始化失败'
                });

                if (initSuccess) {
                    // 测试基础方法
                    const isInitialized = routeManager.isInitialized();
                    results.push({
                        test: 'isInitialized方法',
                        success: isInitialized,
                        message: `返回值: ${isInitialized}`
                    });

                    const userRole = routeManager.getUserRole();
                    results.push({
                        test: 'getUserRole方法',
                        success: userRole !== undefined,
                        message: `用户角色: ${userRole}`
                    });

                    const isAdmin = routeManager.isAdmin();
                    results.push({
                        test: 'isAdmin方法',
                        success: true,
                        message: `是否管理员: ${isAdmin}`
                    });
                }
            } catch (error) {
                results.push({
                    test: '基础测试',
                    success: false,
                    message: `错误: ${error.message}`
                });
            }

            displayResults(resultsDiv, results);
        };

        // 路由权限检查测试
        window.testRoutePermissions = async function() {
            const resultsDiv = document.getElementById('route-results');
            resultsDiv.innerHTML = '<div class="info">正在测试路由权限...</div>';

            if (!routeManager) {
                await initializeManager();
            }

            const testRoutes = [
                '/login',
                '/main',
                '/main/background_manage', // 这个路由在PC端路由配置中有权限设置
                '/main/some_other_route',
                '/webLive',
                '/init'
            ];

            const results = [];

            testRoutes.forEach(route => {
                try {
                    const hasPermission = routeManager.hasPermission(route);
                    const isWhitelist = routeManager.isWhiteListRoute(route);
                    const isProtected = RoutePermissionManager.isProtectedRoute(route);

                    results.push({
                        test: `路由: ${route}`,
                        success: true,
                        message: `权限: ${hasPermission}, 白名单: ${isWhitelist}, 受保护: ${isProtected}`
                    });
                } catch (error) {
                    results.push({
                        test: `路由: ${route}`,
                        success: false,
                        message: `错误: ${error.message}`
                    });
                }
            });

            displayResults(resultsDiv, results);
        };

        // 用户角色模拟测试
        window.simulateUser = async function(role) {
            const resultsDiv = document.getElementById('user-simulation-results');
            resultsDiv.innerHTML = '<div class="info">正在模拟用户角色...</div>';

            currentUser.role = role;

            if (!routeManager) {
                await initializeManager();
            } else {
                routeManager.userInfo = currentUser;
            }

            const roleNames = ['普通用户', '管理员', '超级管理员', '主任'];
            const testRoute = '/main/background_manage';

            const results = [];

            try {
                const hasPermission = routeManager.hasPermission(testRoute);
                const userRole = routeManager.getUserRole();
                const isAdmin = routeManager.isAdmin();
                const isSuperAdmin = routeManager.isSuperAdmin();

                results.push({
                    test: `当前用户角色: ${roleNames[role] || '未知'}`,
                    success: true,
                    message: `角色值: ${userRole}`
                });

                results.push({
                    test: `访问 ${testRoute}`,
                    success: hasPermission,
                    message: hasPermission ? '有权限访问' : '无权限访问'
                });

                results.push({
                    test: '管理员权限检查',
                    success: true,
                    message: `isAdmin: ${isAdmin}, isSuperAdmin: ${isSuperAdmin}`
                });

            } catch (error) {
                results.push({
                    test: '用户角色模拟',
                    success: false,
                    message: `错误: ${error.message}`
                });
            }

            displayResults(resultsDiv, results);
        };

        // 白名单测试
        window.testWhiteList = async function() {
            const resultsDiv = document.getElementById('whitelist-results');
            resultsDiv.innerHTML = '<div class="info">正在测试白名单...</div>';

            if (!routeManager) {
                await initializeManager();
            }

            const whitelistRoutes = ['/login', '/webLive', '/cloudVideoEditChild', '/pacs_login', '/init'];
            const results = [];

            whitelistRoutes.forEach(route => {
                try {
                    const isWhitelist = routeManager.isWhiteListRoute(route);
                    const hasPermission = routeManager.hasPermission(route);

                    results.push({
                        test: `白名单路由: ${route}`,
                        success: isWhitelist && hasPermission,
                        message: `白名单: ${isWhitelist}, 权限: ${hasPermission}`
                    });
                } catch (error) {
                    results.push({
                        test: `白名单路由: ${route}`,
                        success: false,
                        message: `错误: ${error.message}`
                    });
                }
            });

            displayResults(resultsDiv, results);
        };

        // 显示测试结果
        function displayResults(container, results) {
            const html = results.map(result => {
                const className = result.success ? 'success' : 'error';
                return `<div class="test-result ${className}">
                    <strong>${result.test}:</strong> ${result.message}
                </div>`;
            }).join('');

            container.innerHTML = html;
        }

        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testBasicFunctions();
            }, 1000);
        });
    </script>
</body>
</html>
