import BasePermissionManager from './BasePermissionManager.js';
import { USER_ROLE } from './constant.js';

/**
 * 从路由配置中提取带权限信息的路由
 * @param {Array} routes - 路由配置数组
 * @param {string} parentPath - 父路径
 * @returns {Object} 权限路由配置对象
 */
function extractPermissionRoutes(routes, parentPath = '') {
    const permissionRoutes = {};

    if (!routes || !Array.isArray(routes)) {
        return permissionRoutes;
    }

    routes.forEach(route => {
        // 构建完整路径
        let fullPath = parentPath;
        if (route.path) {
            if (route.path.startsWith('/')) {
                fullPath = route.path;
            } else {
                fullPath = parentPath ? `${parentPath}/${route.path}` : `/${route.path}`;
            }
        }

        // 如果路由有权限配置，添加到权限路由中
        if (route.meta && (route.meta.roles || route.meta.permissions)) {
            permissionRoutes[fullPath] = {
                roles: route.meta.roles || [],
                permissions: route.meta.permissions || [],
                strategy: route.meta.strategy || 'OR'
            };
        }

        // 递归处理子路由
        if (route.children && route.children.length > 0) {
            const childPermissionRoutes = extractPermissionRoutes(route.children, fullPath);
            Object.assign(permissionRoutes, childPermissionRoutes);
        }
    });

    return permissionRoutes;
}

/**
 * 路由权限管理器
 * 负责路由级别的权限控制和导航守卫
 */
class RoutePermissionManager extends BasePermissionManager {
    constructor() {
        super();
        // 默认白名单为空，由路由配置决定
        this.whiteList = []; // 白名单路由
        this.routePermissions = new Map(); // 路由权限配置
        this.roleRouteMap = new Map(); // 角色路由映射
        this.routerInstance = null; // 路由实例
        this.extractedRouteConfig = null; // 提取的路由权限配置

        // 设置全局实例
        if (!RoutePermissionManager._globalInstance) {
            RoutePermissionManager._globalInstance = this;
        }
    }

    /**
     * 获取全局实例
     * @returns {RoutePermissionManager} 全局实例
     */
    static getGlobalInstance() {
        if (!RoutePermissionManager._globalInstance) {
            RoutePermissionManager._globalInstance = new RoutePermissionManager();
        }
        return RoutePermissionManager._globalInstance;
    }

    /**
     * 设置全局实例
     * @param {RoutePermissionManager} instance - 实例
     */
    static setGlobalInstance(instance) {
        RoutePermissionManager._globalInstance = instance;
    }

    /**
     * 加载权限数据
     */
    async loadPermissions() {
        // 先尝试加载路由实例
        await this.loadRouterInstance();
        // 加载路由权限配置
        this.loadRoutePermissions();
        // 加载角色路由映射
        this.loadRoleRouteMap();
    }

    /**
     * 动态加载路由实例
     */
    async loadRouterInstance() {
        try {
            // 尝试导入PC端路由配置
            const pcRouterModule = await import('@/module/ultrasync_pc/router/index.js');
            if (pcRouterModule && pcRouterModule.router) {
                this.routerInstance = pcRouterModule.router;
                if (pcRouterModule.whiteList) {
                    this.whiteList = pcRouterModule.whiteList;
                }
                // 提取路由权限配置
                this.extractedRouteConfig = extractPermissionRoutes(this.routerInstance.options.routes);
                console.log('成功加载PC端路由配置', this.extractedRouteConfig);
                return;
            }
        } catch (error) {
            console.warn('无法导入PC端路由配置:', error);
        }

        // 如果PC端配置不可用，尝试移动端配置
        try {
            const mobileRouterModule = await import('@/module/ultrasync/router/index.js');
            if (mobileRouterModule && mobileRouterModule.default) {
                this.routerInstance = mobileRouterModule.default;
                // 移动端没有导出白名单，使用默认白名单
                // 提取路由权限配置
                this.extractedRouteConfig = extractPermissionRoutes(this.routerInstance.options.routes);
                console.log('成功加载移动端路由配置', this.extractedRouteConfig);
                return;
            }
        } catch (error) {
            console.warn('无法导入移动端路由配置:', error);
        }
    }

    /**
     * 加载路由权限配置
     */
    /**
     * 获取路由权限配置表
     * 从路由表中动态获取权限配置，如果没有则使用默认配置
     * @returns {Object} 路由权限配置对象
     */
    static getRoutePermissionConfig() {
        // 优先使用从路由表导入的配置
        const globalInstance = RoutePermissionManager.getGlobalInstance();
        if (globalInstance && globalInstance.extractedRouteConfig) {
            return globalInstance.extractedRouteConfig;
        }

        // 如果没有导入的配置，返回空配置
        // 权限配置应该完全来自路由表的meta字段，而不是硬编码
        return {};
    }

    loadRoutePermissions() {
        // 从统一配置中加载路由权限
        const routePermissions = this.constructor.getRoutePermissionConfig();

        // 将配置存储到Map中
        Object.entries(routePermissions).forEach(([route, config]) => {
            this.routePermissions.set(route, config);
        });
    }

    /**
     * 加载角色路由映射
     */
    loadRoleRouteMap() {
        // 根据路由权限配置生成角色路由映射
        this.roleRouteMap.clear();

        for (let [routePath, config] of this.routePermissions) {
            if (config.roles && config.roles.length > 0) {
                config.roles.forEach(role => {
                    if (!this.roleRouteMap.has(role)) {
                        this.roleRouteMap.set(role, []);
                    }
                    this.roleRouteMap.get(role).push(routePath);
                });
            }
        }
    }

    /**
     * 检查路由权限
     * @param {string} routePath - 路由路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限访问
     */
    hasPermission(routePath, context = {}) {
        // 检查是否已初始化
        if (!this.isInitialized()) {
            console.warn('RoutePermissionManager not initialized');
            return false;
        }

        // 检查白名单
        if (this.isWhiteListRoute(routePath)) {
            return true;
        }

        // 检查登录状态
        if (!this.isLoggedIn()) {
            return false;
        }

        // 检查具体路由权限
        return this.checkRoutePermission(routePath, context);
    }

    /**
     * 检查是否为白名单路由
     * @param {string} routePath - 路由路径
     * @returns {boolean} 是否为白名单路由
     */
    isWhiteListRoute(routePath) {
        return this.whiteList.some(whitePath => routePath.indexOf(whitePath) > -1);
    }

    /**
     * 检查是否已登录
     * @returns {boolean} 是否已登录
     */
    isLoggedIn() {
        // 检查token
        const token = this.getToken();
        return !!token;
    }

    /**
     * 获取登录token
     * @returns {string} token
     */
    getToken() {
        // 使用Tool.getToken()方法
        if (window.Tool && window.Tool.getToken) {
            return window.Tool.getToken();
        }
        return localStorage.getItem('token') || sessionStorage.getItem('token');
    }

    /**
     * 检查具体路由权限
     * @param {string} routePath - 路由路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkRoutePermission(routePath, context = {}) {
        // 获取路由权限配置
        const routeConfig = this.getRouteConfig(routePath);

        if (!routeConfig) {
            // 如果没有配置，默认允许访问
            return true;
        }

        // 获取权限检查策略（默认为OR）
        const strategy = routeConfig.strategy || 'OR';

        let hasRolePermission = false;
        let hasSpecificPermission = false;

        // 检查角色权限
        if (routeConfig.roles && routeConfig.roles.length > 0) {
            const userRole = this.getUserRole();
            hasRolePermission = routeConfig.roles.includes(userRole);
        } else {
            // 如果没有配置角色限制，认为角色权限通过
            hasRolePermission = true;
        }

        // 检查特定权限
        if (routeConfig.permissions && routeConfig.permissions.length > 0) {
            hasSpecificPermission = routeConfig.permissions.every(permission =>
                this.checkSpecificPermission(permission, context)
            );
        } else {
            // 如果没有配置特定权限限制，认为特定权限通过
            hasSpecificPermission = true;
        }

        // 根据策略决定权限检查逻辑
        let hasAccess;
        if (strategy === 'AND') {
            // AND逻辑：角色权限和特定权限都必须满足
            hasAccess = hasRolePermission && hasSpecificPermission;
        } else {
            // OR逻辑：角色权限或特定权限任一满足即可（默认）
            hasAccess = hasRolePermission || hasSpecificPermission;
        }

        if (!hasAccess) {
            const userRole = this.getUserRole();
            this.logPermissionCheck(routePath, false, {
                reason: 'access_denied',
                strategy,
                userRole,
                requiredRoles: routeConfig.roles,
                requiredPermissions: routeConfig.permissions,
                hasRolePermission,
                hasSpecificPermission
            });
            return false;
        }

        this.logPermissionCheck(routePath, true, {
            routeConfig,
            strategy,
            hasRolePermission,
            hasSpecificPermission
        });
        return true;
    }

    /**
     * 获取路由配置
     * @param {string} routePath - 路由路径
     * @returns {Object|null} 路由配置
     */
    getRouteConfig(routePath) {
        // 精确匹配
        if (this.routePermissions.has(routePath)) {
            return this.routePermissions.get(routePath);
        }

        // 模糊匹配
        for (let [configPath, config] of this.routePermissions) {
            if (routePath.startsWith(configPath)) {
                return config;
            }
        }

        return null;
    }

    /**
     * 检查特定权限
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkSpecificPermission(permission, context = {}) {
        switch (permission) {
        case 'admin':
            return this.isAdmin();
        case 'super_admin':
            return this.isSuperAdmin();
        case 'multicenter_access':
            return this.checkMulticenterAccess(context);
        default:
            return true; // 默认允许
        }
    }

    /**
     * 检查多中心访问权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkMulticenterAccess(context = {}) {
        // 基本的多中心访问权限检查
        const userRole = this.getUserRole();
        return userRole >= 1; // 所有登录用户都可以访问多中心
    }

    /**
     * 获取用户可访问的路由列表
     * @returns {Array<string>} 可访问的路由列表
     */
    getAccessibleRoutes() {
        const accessibleRoutes = [];

        // 添加白名单路由
        accessibleRoutes.push(...this.whiteList);

        // 检查配置的路由
        for (let [routePath] of this.routePermissions) {
            if (this.hasPermission(routePath)) {
                accessibleRoutes.push(routePath);
            }
        }

        return accessibleRoutes;
    }

    /**
     * 获取重定向路由
     * @param {string} originalRoute - 原始路由
     * @returns {string} 重定向路由
     */
    getRedirectRoute(originalRoute) {
        // 如果没有登录，重定向到登录页
        if (!this.isLoggedIn()) {
            return '/login';
        }

        // 如果没有权限，重定向到首页或默认页面
        return '/main';
    }

    /**
     * 添加路由权限配置
     * @param {string} routePath - 路由路径
     * @param {Object} config - 权限配置
     */
    addRoutePermission(routePath, config) {
        this.routePermissions.set(routePath, config);
    }

    /**
     * 移除路由权限配置
     * @param {string} routePath - 路由路径
     */
    removeRoutePermission(routePath) {
        this.routePermissions.delete(routePath);
    }

    /**
     * 更新白名单
     * @param {Array<string>} whiteList - 新的白名单
     */
    updateWhiteList(whiteList) {
        this.whiteList = [...whiteList];
    }

    /**
     * 静态方法：检查路由是否需要权限控制
     * @param {string} routePath - 路由路径
     * @returns {boolean} 是否需要权限控制
     */
    static isProtectedRoute(routePath) {
        // 从统一配置中获取需要权限控制的路由列表
        const routePermissionConfig = this.getRoutePermissionConfig();
        const protectedRoutes = Object.keys(routePermissionConfig);

        return protectedRoutes.some(route => routePath === route || routePath.startsWith(route + '/'));
    }

    /**
     * 静态方法：获取路由所需的角色权限
     * @param {string} routePath - 路由路径
     * @returns {Array<number>|null} 所需角色列表，null表示不需要特殊权限
     */
    static getRequiredRoles(routePath) {
        // 从统一配置中获取路由权限配置
        const routePermissionConfig = this.getRoutePermissionConfig();

        // 精确匹配
        if (routePermissionConfig[routePath]) {
            return routePermissionConfig[routePath].roles || null;
        }

        // 模糊匹配（子路由）
        for (const [route, config] of Object.entries(routePermissionConfig)) {
            if (routePath.startsWith(route + '/')) {
                return config.roles || null;
            }
        }

        return null;
    }

    /**
     * 静态方法：获取路由的完整权限配置
     * @param {string} routePath - 路由路径
     * @returns {Object|null} 权限配置对象
     */
    static getRouteConfig(routePath) {
        const routePermissionConfig = this.getRoutePermissionConfig();

        // 精确匹配
        if (routePermissionConfig[routePath]) {
            return routePermissionConfig[routePath];
        }

        // 模糊匹配（子路由）
        for (const [route, config] of Object.entries(routePermissionConfig)) {
            if (routePath.startsWith(route + '/')) {
                return config;
            }
        }

        return null;
    }



    /**
     * 静态方法：检查用户是否有访问指定路由的权限
     * @param {string} routePath - 路由路径
     * @returns {boolean} 是否有权限
     */
    static checkRouteAccess(routePath) {
        // 检查是否是受保护的路由
        if (!this.isProtectedRoute(routePath)) {
            return true; // 不需要特殊权限的路由，允许访问
        }

        // 获取路由配置
        const routeConfig = this.getRouteConfig(routePath);
        if (!routeConfig) {
            return true; // 没有配置，允许访问
        }

        // 使用全局实例
        const managerInstance = this.getGlobalInstance();

        // 检查全局实例是否已初始化
        if (!managerInstance.isInitialized()) {
            console.warn('RoutePermissionManager.checkRouteAccess: 全局实例未初始化，无法进行准确的权限检查');
            return false; // 安全起见，拒绝访问
        }
        // 使用权限管理器实例进行检查
        const userRole = managerInstance.getUserRole();

        // 获取权限检查策略（默认为OR）
        const strategy = routeConfig.strategy || 'OR';

        let hasRolePermission = false;
        let hasSpecificPermission = false;

        // 检查角色权限
        if (routeConfig.roles && routeConfig.roles.length > 0) {
            hasRolePermission = routeConfig.roles.includes(userRole);
        } else {
            // 如果没有配置角色限制，认为角色权限通过
            hasRolePermission = true;
        }

        // 检查特定权限
        if (routeConfig.permissions && routeConfig.permissions.length > 0) {
            // 使用权限管理器实例进行权限检查
            hasSpecificPermission = routeConfig.permissions.every(permission =>
                managerInstance.checkSpecificPermission ?
                    managerInstance.checkSpecificPermission(permission) :
                    this.checkStaticPermissions([permission], userRole)
            );
        } else {
            // 如果没有配置特定权限限制，认为特定权限通过
            hasSpecificPermission = true;
        }

        // 根据策略决定权限检查逻辑
        if (strategy === 'AND') {
            // AND逻辑：角色权限和特定权限都必须满足
            return hasRolePermission && hasSpecificPermission;
        } else {
            // OR逻辑：角色权限或特定权限任一满足即可（默认）
            return hasRolePermission || hasSpecificPermission;
        }
    }



    /**
     * 静态方法：简化的权限检查
     * @param {Array<string>} permissions - 权限列表
     * @param {number} userRole - 用户角色
     * @returns {boolean} 是否有权限
     */
    static checkStaticPermissions(permissions, userRole) {
        return permissions.some(permission => {
            switch (permission) {
            case 'admin':
                return userRole === USER_ROLE.ADMIN ||
                       userRole === USER_ROLE.SUPER_ADMIN ||
                       userRole === USER_ROLE.DIRECTOR; // 管理员、超级管理员、主任
            case 'super_admin':
                return userRole === USER_ROLE.SUPER_ADMIN; // 超级管理员
            default:
                return true; // 默认允许
            }
        });
    }
}

export default RoutePermissionManager;
